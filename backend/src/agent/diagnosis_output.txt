=== IMAGE EXTRACTION DIAGNOSIS ===

1. Testing basic Python functionality...
✅ Basic Python modules work

2. Testing environment loading...
   S3 Key: SET
   Scraper Key: SET
✅ Environment loading works

3. Testing web libraries...
✅ requests imported
✅ BeautifulSoup imported

4. Testing AWS libraries...
✅ boto3 imported

5. Testing ML libraries (optional)...
✅ mediapipe imported
✅ cv2 imported
✅ easyocr imported

6. Testing image extraction import...
🔧 Image Extraction S3 Configuration:
   - S3 Access Key: AKIA4WDC...
   - S3 Region: ap-south-1
   - S3 Bucket: clem-transition-tech
✅ Image extraction module loaded
   MediaPipe: True
   OpenCV: True
   EasyOCR: True

=== SUMMARY ===
Core functionality (web scraping, S3 upload): Should work
ML filtering available: 3/3 libraries

✅ All dependencies available - full functionality enabled
