#!/usr/bin/env python3
"""
Test the actual image extraction function.
"""

import os
import sys

# Add the agent directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_image_extraction_function():
    """Test the actual image extraction function with a real power plant."""
    print("🧪 Testing Image Extraction Function")
    print("=" * 50)
    
    try:
        # Import the image extraction module
        import image_extraction
        
        print("✅ Image extraction module imported successfully")
        print(f"   S3 Access Key: {'SET' if image_extraction.AWS_ACCESS_KEY else 'NOT SET'}")
        print(f"   S3 Bucket: {image_extraction.S3_BUCKET}")
        print(f"   Scraper API Key: {'SET' if image_extraction.SCRAPER_API_KEY else 'NOT SET'}")
        print(f"   MediaPipe: {image_extraction.MEDIAPIPE_AVAILABLE}")
        print(f"   OpenCV: {image_extraction.CV2_AVAILABLE}")
        print(f"   EasyOCR: {image_extraction.EASYOCR_AVAILABLE}")
        
        # Test with a well-known power plant
        test_plant = "Stanwell Power Station"
        session_id = "test_session"
        
        print(f"\n🖼️ Testing image extraction for: {test_plant}")
        print("This may take a few minutes...")
        
        # Run the image extraction
        s3_urls = image_extraction.extract_and_upload_images(test_plant, session_id)
        
        print(f"\n📊 Results:")
        print(f"   Images extracted and uploaded: {len(s3_urls)}")
        
        if s3_urls:
            print("   S3 URLs:")
            for i, url in enumerate(s3_urls, 1):
                print(f"     {i}. {url}")
            print("✅ Image extraction completed successfully!")
        else:
            print("⚠️ No images were extracted/uploaded")
            
        return len(s3_urls) > 0
        
    except Exception as e:
        print(f"❌ Image extraction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_functions():
    """Test individual functions in the image extraction module."""
    print("\n🔧 Testing Individual Functions")
    print("=" * 50)
    
    try:
        import image_extraction
        
        # Test 1: Google search function
        print("1. Testing Google search...")
        try:
            if image_extraction.SCRAPER_API_KEY:
                results = image_extraction.search_google("Stanwell Power Station", 1, 3)
                print(f"   Found {len(results)} search results")
                if results:
                    print(f"   First result: {results[0]}")
                    print("✅ Google search works")
                else:
                    print("⚠️ No search results returned")
            else:
                print("⚠️ Scraper API key not set - skipping search test")
        except Exception as e:
            print(f"❌ Google search failed: {e}")
        
        # Test 2: S3 upload function (with dummy data)
        print("\n2. Testing S3 upload function...")
        try:
            # Create a temporary test folder with a dummy file
            import tempfile
            import shutil
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create a dummy image file
                dummy_file = os.path.join(temp_dir, "test_image.txt")
                with open(dummy_file, 'w') as f:
                    f.write("This is a test file")
                
                # Test upload function
                s3_urls = image_extraction.upload_folder_to_s3(
                    local_folder=temp_dir,
                    bucket_name=image_extraction.S3_BUCKET,
                    aws_access_key=image_extraction.AWS_ACCESS_KEY,
                    aws_secret_key=image_extraction.AWS_SECRET_KEY,
                    aws_region=image_extraction.AWS_REGION,
                    s3_prefix="test_upload",
                    max_images=1
                )
                
                if s3_urls:
                    print(f"✅ S3 upload works - uploaded to: {s3_urls[0]}")
                else:
                    print("❌ S3 upload failed - no URLs returned")
                    
        except Exception as e:
            print(f"❌ S3 upload test failed: {e}")
        
        # Test 3: Image filtering functions
        print("\n3. Testing image filtering functions...")
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create dummy image files
                for i in range(3):
                    dummy_file = os.path.join(temp_dir, f"test_image_{i}.jpg")
                    with open(dummy_file, 'wb') as f:
                        # Write some dummy binary data
                        f.write(b'\x89PNG\r\n\x1a\n' + b'dummy image data' * 100)
                
                print(f"   Created {len(os.listdir(temp_dir))} test files")
                
                # Test face and text filtering
                image_extraction.face_and_text_filter(temp_dir)
                print(f"   After filtering: {len(os.listdir(temp_dir))} files remain")
                
                # Test quality filtering
                image_extraction.remove_low_quality(temp_dir)
                print(f"   After quality filter: {len(os.listdir(temp_dir))} files remain")
                
                print("✅ Image filtering functions work")
                
        except Exception as e:
            print(f"❌ Image filtering test failed: {e}")
            
    except Exception as e:
        print(f"❌ Individual function tests failed: {e}")

def main():
    """Run all tests."""
    print("🧪 Image Extraction Function Tests")
    print("=" * 60)
    
    # Test individual functions first
    test_individual_functions()
    
    # Ask user if they want to run full extraction test
    print("\n" + "=" * 60)
    print("⚠️ Full image extraction test will:")
    print("   - Search Google for images")
    print("   - Download images from websites")
    print("   - Process and filter images")
    print("   - Upload to S3")
    print("   - May take several minutes")
    
    response = input("\nRun full image extraction test? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        success = test_image_extraction_function()
        if success:
            print("\n🎉 All image extraction tests passed!")
        else:
            print("\n⚠️ Some tests failed - check output above")
    else:
        print("\n✅ Individual function tests completed")
        print("💡 To test full extraction, run this script again and choose 'y'")

if __name__ == "__main__":
    main()
