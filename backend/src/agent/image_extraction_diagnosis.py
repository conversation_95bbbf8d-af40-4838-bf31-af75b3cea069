#!/usr/bin/env python3
"""
Simple diagnostic script for image extraction issues.
"""

import os
import sys

def main():
    print("=== IMAGE EXTRACTION DIAGNOSIS ===")
    
    # Test 1: Basic Python functionality
    print("\n1. Testing basic Python functionality...")
    try:
        import json
        import urllib.parse
        print("✅ Basic Python modules work")
    except Exception as e:
        print(f"❌ Basic Python modules failed: {e}")
        return
    
    # Test 2: Environment loading
    print("\n2. Testing environment loading...")
    try:
        from dotenv import load_dotenv
        env_path = os.path.join(os.path.dirname(__file__), '../../.env')
        load_dotenv(env_path)
        
        s3_key = os.getenv('S3_AWS_ACCESS_KEY_ID')
        scraper_key = os.getenv('SCRAPER_API_KEY')
        
        print(f"   S3 Key: {'SET' if s3_key else 'NOT SET'}")
        print(f"   Scraper Key: {'SET' if scraper_key else 'NOT SET'}")
        print("✅ Environment loading works")
    except Exception as e:
        print(f"❌ Environment loading failed: {e}")
    
    # Test 3: Basic web libraries
    print("\n3. Testing web libraries...")
    try:
        import requests
        print("✅ requests imported")
    except Exception as e:
        print(f"❌ requests failed: {e}")
    
    try:
        from bs4 import BeautifulSoup
        print("✅ BeautifulSoup imported")
    except Exception as e:
        print(f"❌ BeautifulSoup failed: {e}")
    
    # Test 4: AWS libraries
    print("\n4. Testing AWS libraries...")
    try:
        import boto3
        print("✅ boto3 imported")
    except Exception as e:
        print(f"❌ boto3 failed: {e}")
    
    # Test 5: Heavy ML libraries (optional)
    print("\n5. Testing ML libraries (optional)...")
    
    ml_libs = ['mediapipe', 'cv2', 'easyocr']
    available_libs = []
    
    for lib in ml_libs:
        try:
            __import__(lib)
            print(f"✅ {lib} imported")
            available_libs.append(lib)
        except Exception as e:
            print(f"❌ {lib} failed: {e}")
    
    # Test 6: Image extraction import
    print("\n6. Testing image extraction import...")
    try:
        # Try to import without executing heavy code
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "image_extraction", 
            os.path.join(os.path.dirname(__file__), "image_extraction.py")
        )
        if spec and spec.loader:
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            print("✅ Image extraction module loaded")
            
            # Check availability flags
            if hasattr(module, 'MEDIAPIPE_AVAILABLE'):
                print(f"   MediaPipe: {module.MEDIAPIPE_AVAILABLE}")
            if hasattr(module, 'CV2_AVAILABLE'):
                print(f"   OpenCV: {module.CV2_AVAILABLE}")
            if hasattr(module, 'EASYOCR_AVAILABLE'):
                print(f"   EasyOCR: {module.EASYOCR_AVAILABLE}")
                
        else:
            print("❌ Could not create module spec")
            
    except Exception as e:
        print(f"❌ Image extraction import failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Summary
    print("\n=== SUMMARY ===")
    print("Core functionality (web scraping, S3 upload): Should work")
    print(f"ML filtering available: {len(available_libs)}/{len(ml_libs)} libraries")
    
    if len(available_libs) == 0:
        print("\n💡 RECOMMENDATION:")
        print("   Use lightweight image extraction without ML filtering")
        print("   Or install ML dependencies:")
        print("   pip install mediapipe opencv-python easyocr")
    elif len(available_libs) < len(ml_libs):
        print("\n💡 RECOMMENDATION:")
        print("   Some ML libraries missing - partial filtering available")
        print("   Install missing libraries for full functionality")
    else:
        print("\n✅ All dependencies available - full functionality enabled")

if __name__ == "__main__":
    main()
