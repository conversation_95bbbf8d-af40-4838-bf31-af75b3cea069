import requests
from bs4 import BeautifulSoup
import os
import certifi
from urllib.parse import urljoin, urlparse
import boto3
from dotenv import load_dotenv

# Optional heavy dependencies - import with error handling
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    print("⚠️ MediaPipe not available - face detection will be skipped")
    MEDIAPIPE_AVAILABLE = False

try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    print("⚠️ OpenCV not available - image processing will be limited")
    CV2_AVAILABLE = False

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    print("⚠️ EasyOCR not available - text detection will be skipped")
    EASYOCR_AVAILABLE = False

# Load environment variables from the correct path
load_dotenv(os.path.join(os.path.dirname(__file__), '../../.env'))

# ========== CONFIG ==========
# Use S3-specific credentials for image storage
AWS_ACCESS_KEY = os.getenv('S3_AWS_ACCESS_KEY_ID')
AWS_SECRET_KEY = os.getenv('S3_AWS_SECRET_ACCESS_KEY')
AWS_REGION = os.getenv('AWS_REGION', 'ap-south-1')
S3_BUCKET = 'clem-transition-tech'  # Use the correct S3 bucket
SCRAPER_API_KEY = os.getenv('SCRAPER_API_KEY')

# ========== IMAGE EXTRACTION LIMITS ==========
# Configurable limits to control resource usage and costs
MAX_SEARCH_RESULTS = int(os.getenv('MAX_SEARCH_RESULTS', '5'))      # Google search results
MAX_INTERNAL_LINKS = int(os.getenv('MAX_INTERNAL_LINKS', '10'))     # Internal links per site (updated to 10)
MAX_SITES_TO_PROCESS = int(os.getenv('MAX_SITES_TO_PROCESS', '2'))  # Max sites to process (updated to 2)
MAX_FINAL_IMAGES = int(os.getenv('MAX_FINAL_IMAGES', '5'))          # Max images uploaded to S3

# Debug logging for configuration
print(f"🔧 Image Extraction Configuration:")
print(f"   - S3 Access Key: {AWS_ACCESS_KEY[:8] + '...' if AWS_ACCESS_KEY else 'NOT SET'}")
print(f"   - S3 Region: {AWS_REGION}")
print(f"   - S3 Bucket: {S3_BUCKET}")
print(f"   - Max Search Results: {MAX_SEARCH_RESULTS}")
print(f"   - Max Internal Links: {MAX_INTERNAL_LINKS}")
print(f"   - Max Sites to Process: {MAX_SITES_TO_PROCESS}")
print(f"   - Max Final Images: {MAX_FINAL_IMAGES}")
# ============================

def search_google(query: str, page: int, num_results=None):
    """Search Google for results with configurable limit."""
    if num_results is None:
        num_results = MAX_SEARCH_RESULTS
    payload = {'api_key': SCRAPER_API_KEY, 'query': query, 'page': page, 'num': num_results}
    try:
        response = requests.get('https://api.scraperapi.com/structured/google/search', params=payload, verify=certifi.where())
        response.raise_for_status()
        results = response.json()
        links = []
        for result in results.get('organic_results', []):
            link = result.get('link', '')
            if link:
                links.append(link)
        return links
    except Exception as e:
        print(f"Error during Google search: {e}")
        return []

def get_internal_links(base_url, url, max_links=None):
    """Get internal links from a URL with configurable limit."""
    if max_links is None:
        max_links = MAX_INTERNAL_LINKS
    EXCLUDED_EXTENSIONS = ('.pdf', '.jpg', '.jpeg', '.png', '.gif', '.svg', '.doc', '.docx',
        '.xls', '.xlsx', '.ppt', '.pptx', '.zip', '.rar', '.tar.gz', '.mp4', '.mp3', '.wav')
    try:
        response = requests.get(url)
        LINKS = []
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, "html.parser")
            internal_links = []
            for a in soup.find_all('a', href=True):
                href = a['href'].split('?')[0].split('#')[0].strip()
                full_url = urljoin(base_url, href)
                if full_url.startswith(base_url) and not href.lower().endswith(EXCLUDED_EXTENSIONS):
                    if full_url not in internal_links:
                        internal_links.append(full_url)
                        if len(internal_links) >= max_links:
                            break
            for link in internal_links:
                LINKS.append(link)
        return LINKS
    except Exception as e:
        print(f"Exception in get_internal_links for {url}: {e}")
        return []

def get_images(base_url, LINKS, image_folder):
    if len(LINKS) > 0:
        downloaded_images = set()
        os.makedirs(image_folder, exist_ok=True)
        for link in LINKS:
            try:
                response = requests.get(link)
            except Exception as e:
                continue
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, "html.parser")
                img_tags = soup.find_all('img')
                for img in img_tags:
                    img_url = img.get('src')
                    if not img_url:
                        continue
                    img_url = urljoin(base_url, img_url.split('?')[0].split('#')[0])
                    filename = os.path.basename(urlparse(img_url).path)
                    if not filename:
                        continue
                    filepath = os.path.join(image_folder, filename)
                    if img_url in downloaded_images:
                        continue
                    downloaded_images.add(img_url)
                    try:
                        img_resp = requests.get(img_url, timeout=10)
                        img_resp.raise_for_status()
                        with open(filepath, 'wb') as f:
                            f.write(img_resp.content)
                    except Exception:
                        continue

def face_and_text_filter(image_folder):
    """Filter out images with faces and text. Gracefully handles missing dependencies."""

    if not os.path.exists(image_folder):
        print(f"⚠️ Image folder not found: {image_folder}")
        return

    # Pre-filter: Remove images with problematic formats that cause broadcasting errors
    if CV2_AVAILABLE:
        try:
            print("🔧 Pre-filtering problematic image formats...")
            image_files = os.listdir(image_folder)
            removed_count = 0

            for image_file in image_files:
                image_path = os.path.join(image_folder, image_file)
                try:
                    img = cv2.imread(image_path)
                    if img is None:
                        os.remove(image_path)
                        removed_count += 1
                        continue

                    # Remove images with unusual channel configurations
                    if len(img.shape) != 3 or img.shape[2] not in [3, 4]:
                        print(f"⚠️ Removing image with unusual shape: {img.shape} - {image_file}")
                        os.remove(image_path)
                        removed_count += 1
                        continue

                    # Remove images with unusual data types
                    if img.dtype not in ['uint8', 'uint16']:
                        print(f"⚠️ Removing image with unusual dtype: {img.dtype} - {image_file}")
                        os.remove(image_path)
                        removed_count += 1
                        continue

                except Exception as e:
                    print(f"⚠️ Error checking image {image_file}: {e}")
                    try:
                        os.remove(image_path)
                        removed_count += 1
                    except:
                        pass

            print(f"✅ Pre-filtering complete - removed {removed_count} problematic images")
        except Exception as e:
            print(f"⚠️ Pre-filtering failed: {e}")

    # Face removal (only if MediaPipe and OpenCV are available)
    if MEDIAPIPE_AVAILABLE and CV2_AVAILABLE:
        try:
            mp_face = mp.solutions.face_detection
            image_files = os.listdir(image_folder)
            with mp_face.FaceDetection(model_selection=1, min_detection_confidence=0.5) as face_detection:
                for image_link in image_files:
                    image_path = os.path.join(image_folder, image_link)
                    try:
                        img = cv2.imread(image_path)
                        if img is None:
                            continue

                        # Validate image shape and channels
                        if len(img.shape) != 3 or img.shape[2] != 3:
                            print(f"⚠️ Skipping image with invalid shape: {img.shape}")
                            continue

                        # Ensure image is in correct format for face detection
                        if img.dtype != 'uint8':
                            img = img.astype('uint8')

                        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                        results = face_detection.process(img_rgb)
                        if results.detections:
                            os.remove(image_path)
                    except Exception as e:
                        print(f"⚠️ Error processing image {image_link}: {e}")
                        continue
            print("✅ Face detection filtering completed")
        except Exception as e:
            print(f"⚠️ Face detection failed: {e}")
    else:
        print("⚠️ Skipping face detection - dependencies not available")

    # Text removal (only if EasyOCR is available)
    if EASYOCR_AVAILABLE:
        try:
            reader = easyocr.Reader(['en'])
            image_files = os.listdir(image_folder)
            for image_link in image_files:
                image_path = os.path.join(image_folder, image_link)
                if not os.path.exists(image_path):  # May have been removed by face detection
                    continue

                try:
                    # Validate image before EasyOCR processing
                    if CV2_AVAILABLE:
                        img = cv2.imread(image_path)
                        if img is None:
                            print(f"⚠️ Skipping unreadable image: {image_link}")
                            continue

                        # Check for unusual channel configurations that cause broadcasting errors
                        if len(img.shape) != 3 or img.shape[2] not in [1, 3, 4]:
                            print(f"⚠️ Skipping image with unusual channels: {img.shape} - {image_link}")
                            try:
                                os.remove(image_path)  # Remove problematic images
                            except:
                                pass
                            continue

                    # Process with EasyOCR
                    results = reader.readtext(image_path)
                    if results:
                        os.remove(image_path)

                except Exception as e:
                    print(f"⚠️ Error processing image {image_link} with EasyOCR: {e}")
                    # Remove problematic images that cause errors
                    try:
                        os.remove(image_path)
                    except:
                        pass
                    continue

            print("✅ Text detection filtering completed")
        except Exception as e:
            print(f"⚠️ Text detection failed: {e}")
    else:
        print("⚠️ Skipping text detection - EasyOCR not available")

def remove_low_quality(image_folder):
    """Remove low quality images. Gracefully handles missing OpenCV."""

    if not CV2_AVAILABLE:
        print("⚠️ Skipping quality filtering - OpenCV not available")
        return

    try:
        VARIANCE_THRESHOLD = 100
        MIN_RESOLUTION = 300
        removed_count = 0

        for filename in os.listdir(image_folder):
            filepath = os.path.join(image_folder, filename)
            if os.path.isfile(filepath):
                try:
                    image = cv2.imread(filepath)
                    if image is None:
                        os.remove(filepath)
                        removed_count += 1
                        continue

                    # Validate image shape and channels
                    if len(image.shape) < 2:
                        print(f"⚠️ Removing image with invalid shape: {image.shape}")
                        os.remove(filepath)
                        removed_count += 1
                        continue

                    h, w = image.shape[:2]
                    if w < MIN_RESOLUTION or h < MIN_RESOLUTION:
                        os.remove(filepath)
                        removed_count += 1
                        continue

                    # Convert to grayscale safely
                    if len(image.shape) == 3 and image.shape[2] == 3:
                        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                    elif len(image.shape) == 3 and image.shape[2] == 4:
                        gray = cv2.cvtColor(image, cv2.COLOR_BGRA2GRAY)
                    elif len(image.shape) == 2:
                        gray = image
                    else:
                        print(f"⚠️ Unsupported image format: {image.shape}")
                        os.remove(filepath)
                        removed_count += 1
                        continue

                    variance = cv2.Laplacian(gray, cv2.CV_64F).var()
                    if variance < VARIANCE_THRESHOLD:
                        os.remove(filepath)
                        removed_count += 1

                except Exception as e:
                    print(f"⚠️ Error processing image {filename}: {e}")
                    try:
                        os.remove(filepath)
                        removed_count += 1
                    except:
                        pass

        print(f"✅ Quality filtering completed - removed {removed_count} low quality images")

    except Exception as e:
        print(f"⚠️ Quality filtering failed: {e}")

def upload_folder_to_s3(local_folder, bucket_name, aws_access_key, aws_secret_key, aws_region="us-east-1", s3_prefix=None, max_images=None):
    """
    Uploads files from a local folder to S3 bucket with a maximum limit.

    Args:
        max_images (int): Maximum number of images to upload (uses MAX_FINAL_IMAGES if None)
    """
    if max_images is None:
        max_images = MAX_FINAL_IMAGES
    s3 = boto3.client(
        's3',
        aws_access_key_id=aws_access_key,
        aws_secret_access_key=aws_secret_key,
        region_name=aws_region
    )
    s3_links = []
    uploaded_count = 0
    
    # Collect all files first
    all_files = []
    for root, dirs, files in os.walk(local_folder):
        for file in files:
            local_path = os.path.join(root, file)
            all_files.append(local_path)
    
    # Sort files to ensure consistent selection (optional: by size, name, etc.)
    all_files.sort()
    
    # Upload only up to max_images
    for local_path in all_files:
        if uploaded_count >= max_images:
            print(f"⚠️  Reached maximum limit of {max_images} images. Skipping remaining {len(all_files) - uploaded_count} images.")
            break
            
        relative_path = os.path.relpath(local_path, local_folder)
        s3_key = os.path.join(s3_prefix, relative_path) if s3_prefix else relative_path
        s3_key = s3_key.replace("\\", "/")  # Windows fix
        
        try:
            s3.upload_file(local_path, bucket_name, s3_key)
            s3_url = f"https://{bucket_name}.s3.amazonaws.com/{s3_key}"
            s3_links.append(s3_url)
            uploaded_count += 1
            print(f"✅ Uploaded ({uploaded_count}/{max_images}): {os.path.basename(local_path)}")
        except Exception as e:
            print(f"❌ Failed to upload {os.path.basename(local_path)}: {e}")
    
    print(f"🎉 Successfully uploaded {uploaded_count} out of {len(all_files)} total images to S3")
    return s3_links

def extract_and_upload_images(powerplant: str, session_id: str = "unknown") -> list:
    """
    Extract images for a power plant and upload to S3.
    
    Args:
        powerplant (str): Name of the power plant
        session_id (str): Session ID for logging
        
    Returns:
        list: List of S3 URLs for uploaded images
    """
    print(f"[Session {session_id}] 🖼️ Starting image extraction for: {powerplant}")
    
    try:
        query = powerplant
        page = 1

        # Search & download logic
        result_urls = search_google(query, page)
        if not result_urls:
            print(f"[Session {session_id}] ❌ No search results found for {powerplant}")
            return []
            
        all_links_to_scrape = []
        image_folder = f"{powerplant.replace(' ', '_')}_images_{session_id}"

        # Process search results with different logic for Wikipedia vs non-Wikipedia
        # Wikipedia pages about the specific power plant are valuable for images
        wikipedia_urls = []
        non_wikipedia_urls = []

        for url in result_urls:
            parsed_url = urlparse(url)
            if 'wikipedia.org' in parsed_url.netloc:
                wikipedia_urls.append(url)
            else:
                non_wikipedia_urls.append(url)

        print(f"[Session {session_id}] � Found {len(wikipedia_urls)} Wikipedia and {len(non_wikipedia_urls)} non-Wikipedia pages")

        # Process Wikipedia pages (main page only, no internal links)
        # Wikipedia pages about the specific power plant contain relevant images
        wikipedia_to_process = wikipedia_urls[:1]  # Take only first Wikipedia page (usually the most relevant)
        for url in wikipedia_to_process:
            parsed_url = urlparse(url)
            all_links_to_scrape.append(url)  # Only main page, no internal links
            print(f"[Session {session_id}] 📖 Added Wikipedia main page for power plant: {parsed_url.netloc}")
            print(f"[Session {session_id}] 📖 Wikipedia URL: {url}")

        # Process non-Wikipedia sites (with internal links)
        non_wikipedia_to_process = non_wikipedia_urls[:MAX_SITES_TO_PROCESS]
        for url in non_wikipedia_to_process:
            parsed_url = urlparse(url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

            # Add main page
            links = [url]

            # Get internal links (up to MAX_INTERNAL_LINKS per site)
            internal_links = get_internal_links(base_url, url)
            links.extend(internal_links)

            all_links_to_scrape.extend(links)
            print(f"[Session {session_id}] 📄 Added {len(links)} links from {parsed_url.netloc} (1 main + {len(internal_links)} internal)")

        all_links_to_scrape = list(set(all_links_to_scrape))
        print(f"[Session {session_id}] 🔍 Found {len(all_links_to_scrape)} links to scrape for images")
        
        for link in all_links_to_scrape:
            parsed_url = urlparse(link)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            get_images(base_url, [link], image_folder)

        # Filtering
        if os.path.exists(image_folder):
            print(f"[Session {session_id}] 🔧 Filtering images (removing faces, text, low quality)")
            face_and_text_filter(image_folder)
            remove_low_quality(image_folder)

        # Upload to S3 and get URLs (maximum 5 images)
        s3_links = upload_folder_to_s3(
            local_folder=image_folder,
            bucket_name=S3_BUCKET,
            aws_access_key=AWS_ACCESS_KEY,
            aws_secret_key=AWS_SECRET_KEY,
            aws_region=AWS_REGION,
            s3_prefix=f"{image_folder}_{session_id}",
            max_images=MAX_FINAL_IMAGES  # Configurable image limit
        )

        print(f"[Session {session_id}] ✅ Successfully uploaded {len(s3_links)} images for {powerplant}")
        
        # Clean up local folder after upload
        try:
            import shutil
            if os.path.exists(image_folder):
                shutil.rmtree(image_folder)
                print(f"[Session {session_id}] 🧹 Cleaned up local image folder")
        except Exception as e:
            print(f"[Session {session_id}] ⚠️ Could not clean up folder: {e}")
        
        return s3_links
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ Error in image extraction: {e}")
        return []

def main():
    """CLI interface for standalone usage"""
    powerplant = input("Enter the power plant name: ").strip()
    s3_links = extract_and_upload_images(powerplant, "cli")
    
    print(f"Uploaded {len(s3_links)} images. S3 links:")
    for url in s3_links:
        print(url)
    return s3_links

if __name__ == "__main__":
    main()
