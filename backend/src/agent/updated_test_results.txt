🧪 Testing Updated Image Extraction Logic
🚫 Wikipedia pages will be ignored
🎯 Only first 2 non-Wikipedia sites will be processed
🔗 Up to 10 internal links per site will be extracted
================================================================================
🧪 Testing Updated Image Extraction Logic
============================================================
🔧 Image Extraction Configuration:
   - S3 Access Key: AKIA4WDC...
   - S3 Region: ap-south-1
   - S3 Bucket: clem-transition-tech
   - Max Search Results: 5
   - Max Internal Links: 10
   - Max Sites to Process: 2
   - Max Final Images: 5
✅ Image extraction module imported successfully
   Max Search Results: 5
   Max Internal Links: 10
   Max Sites to Process: 2
   Max Final Images: 5

🔍 Testing Google search...
   Found 5 search results:
     1. ✅ NON-WIKI: https://www.stanwell.com/stanwell-power-station
     2. 🚫 WIKIPEDIA: https://en.wikipedia.org/wiki/Stanwell_Power_Station
     3. ✅ NON-WIKI: https://www.stanwell.com/generation-assets
     4. ✅ NON-WIKI: https://www.gem.wiki/Stanwell_power_station
     5. ✅ NON-WIKI: https://www.wsp.com/en-au/projects/stanwell-power-station

📊 Results Summary:
   Wikipedia pages: 1
   Non-Wikipedia pages: 4
   Will process: 2 sites

🎯 Testing filtering logic...
   Selected 2 non-Wikipedia URLs:
     1. www.stanwell.com
     2. www.stanwell.com

🔗 Testing internal link extraction...
   Testing site: www.stanwell.com
   Found 10 internal links (max: 10)
   Sample internal links:
     1. https://www.stanwell.com
     2. https://www.stanwell.com/
     3. https://www.stanwell.com/about-us
     ... and 7 more

📈 Testing Link Calculation Logic
============================================================

🎭 Scenario 1: No Wikipedia in results
   Total search results: 5
   Wikipedia pages (ignored): 0
   Non-Wikipedia pages: 5
   Sites to process: 2
   Estimated total links: 22
   Links per site: 1 main + 10 internal = 11

🎭 Scenario 2: Wikipedia first result
   Total search results: 5
   Wikipedia pages (ignored): 1
   Non-Wikipedia pages: 4
   Sites to process: 2
   Estimated total links: 22
   Links per site: 1 main + 10 internal = 11

🎭 Scenario 3: Multiple Wikipedia results
   Total search results: 5
   Wikipedia pages (ignored): 2
   Non-Wikipedia pages: 3
   Sites to process: 2
   Estimated total links: 22
   Links per site: 1 main + 10 internal = 11

================================================================================
📋 Test Results Summary:
   Wikipedia Filtering Test: ✅ PASS
   Link Calculation Test: ✅ PASS

🎉 All tests passed!

📋 Updated Logic Summary:
   ✅ Wikipedia pages are completely ignored
   ✅ First 2 non-Wikipedia sites are processed
   ✅ Up to 10 internal links per site are extracted
   ✅ Maximum ~22 links total (2 sites × 11 links each)
