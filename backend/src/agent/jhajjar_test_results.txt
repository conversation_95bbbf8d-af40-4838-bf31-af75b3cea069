🧪 Jhajjar Power Plant - Image Extraction Test
🚫 Wikipedia pages will be ignored
🎯 First 2 non-Wikipedia sites will be processed
🔗 Up to 10 internal links per site will be extracted
================================================================================
🧪 Testing Jhajjar Power Plant - Search Phase
============================================================
🔧 Image Extraction Configuration:
   - S3 Access Key: AKIA4WDC...
   - S3 Region: ap-south-1
   - S3 Bucket: clem-transition-tech
   - Max Search Results: 5
   - Max Internal Links: 10
   - Max Sites to Process: 2
   - Max Final Images: 5
🔍 Searching for: Jhajjar Power Plant
✅ Found 4 search results

📋 Search Results Analysis:
   1. ✅ NON-WIKI: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
   2. ✅ NON-WIKI: https://apraava.com/projects/jhajjar-power-plant
   3. 🚫 WIKIPEDIA (ignored): https://en.wikipedia.org/wiki/Jhajjar_Power_Station
   4. ✅ NON-WIKI: https://www.apraava.com/newsroom/clp-india%E2%80%99s-jhajjar-power-limited-is-the-cleanest-domestic-coal-fired-power-plant-in-ncr

📊 Filtering Results:
   Total results: 4
   Wikipedia pages (ignored): 1
   Non-Wikipedia pages: 3
   Selected for processing: 2

🎯 Sites to be processed:
   1. www.clpgroup.com
   2. apraava.com

🔗 Testing Internal Link Extraction
============================================================

🌐 Site 1: www.clpgroup.com
   Main URL: https://www.clpgroup.com/en/about/our-business/assets-and-services/india/jhajjar-power-station.html
   Internal links found: 2
   Total links for this site: 3
   Sample internal links:
     1. https://www.clpgroup.com
     2. https://www.clpgroup.com/en/pages/wechat.html

🌐 Site 2: apraava.com
   Main URL: https://apraava.com/projects/jhajjar-power-plant
Exception in get_internal_links for https://apraava.com/projects/jhajjar-power-plant: HTTPSConnectionPool(host='apraava.com', port=443): Max retries exceeded with url: /projects/jhajjar-power-plant (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1010)')))
   Internal links found: 0
   Total links for this site: 1

📈 Total Link Summary:
   Sites processed: 2
   Total links collected: 4
   Average links per site: 2.0

🖼️ Testing Full Image Extraction
============================================================
🚀 Starting full image extraction for: Jhajjar Power Plant
⚠️ This will:
   - Search Google for results
   - Filter out Wikipedia pages
   - Process first 2 non-Wikipedia sites
   - Extract up to 10 internal links per site
   - Download images from all links
   - Apply ML filtering (faces, text, quality)
   - Upload up to 5 images to S3
   - This may take several minutes...

Proceed with full extraction? (y/N): Traceback (most recent call last):
  File "/Users/<USER>/Documents/SE-TRANSITION-AGI-TECH/backend/src/agent/test_jhajjar_power_plant.py", line 219, in <module>
    main()
  File "/Users/<USER>/Documents/SE-TRANSITION-AGI-TECH/backend/src/agent/test_jhajjar_power_plant.py", line 202, in main
    test_jhajjar_full_extraction()
  File "/Users/<USER>/Documents/SE-TRANSITION-AGI-TECH/backend/src/agent/test_jhajjar_power_plant.py", line 149, in test_jhajjar_full_extraction
    response = input(f"\nProceed with full extraction? (y/N): ").strip().lower()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
KeyboardInterrupt
