WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
I0000 00:00:1752641237.907864  283149 gl_context.cc:369] GL version: 2.1 (2.1 Metal - 88.1), renderer: Apple M2
[ WARN:0@11.991] global grfmt_png.cpp:695 read_chunk chunk data is too large
INFO: Created TensorFlow Lite XNNPACK delegate for CPU.
W0000 00:00:1752641237.946611  283405 inference_feedback_manager.cc:114] Feedback manager requires a model with a single signature inference. Disabling support for feedback tensors.
[ WARN:0@12.269] global grfmt_png.cpp:695 read_chunk chunk data is too large
[ WARN:0@12.442] global grfmt_png.cpp:695 read_chunk chunk data is too large
[ WARN:0@14.836] global grfmt_png.cpp:695 read_chunk chunk data is too large
chunk too big
[ WARN:0@15.420] global grfmt_png.cpp:695 read_chunk chunk data is too large
[ WARN:0@15.607] global grfmt_png.cpp:695 read_chunk chunk data is too large
[ WARN:0@15.782] global grfmt_png.cpp:695 read_chunk chunk data is too large
🧪 Image Extraction Function Tests
============================================================

🔧 Testing Individual Functions
==================================================
🔧 Image Extraction S3 Configuration:
   - S3 Access Key: AKIA4WDC...
   - S3 Region: ap-south-1
   - S3 Bucket: clem-transition-tech
1. Testing Google search...
   Found 3 search results
   First result: https://www.stanwell.com/stanwell-power-station
✅ Google search works

2. Testing S3 upload function...
✅ Uploaded (1/1): test_image.txt
🎉 Successfully uploaded 1 out of 1 total images to S3
✅ S3 upload works - uploaded to: https://clem-transition-tech.s3.amazonaws.com/test_upload/test_image.txt

3. Testing image filtering functions...
   Created 3 test files
✅ Face detection filtering completed
⚠️ Text detection failed: [Errno 1] Operation not permitted; last error log: [png] chunk too big
   After filtering: 3 files remain
✅ Quality filtering completed - removed 3 low quality images
   After quality filter: 0 files remain
✅ Image filtering functions work

============================================================
⚠️ Full image extraction test will:
   - Search Google for images
   - Download images from websites
   - Process and filter images
   - Upload to S3
   - May take several minutes

Run full image extraction test? (y/N): 
✅ Individual function tests completed
💡 To test full extraction, run this script again and choose 'y'
