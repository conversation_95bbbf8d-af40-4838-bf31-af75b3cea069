#!/usr/bin/env python3
"""
Test the updated image extraction logic that ignores Wikipedia.
"""

import os
import sys

# Add the agent directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_wikipedia_filtering():
    """Test the Wikipedia filtering logic."""
    print("🧪 Testing Updated Image Extraction Logic")
    print("=" * 60)
    
    try:
        import image_extraction
        from urllib.parse import urlparse
        
        print("✅ Image extraction module imported successfully")
        print(f"   Max Search Results: {image_extraction.MAX_SEARCH_RESULTS}")
        print(f"   Max Internal Links: {image_extraction.MAX_INTERNAL_LINKS}")
        print(f"   Max Sites to Process: {image_extraction.MAX_SITES_TO_PROCESS}")
        print(f"   Max Final Images: {image_extraction.MAX_FINAL_IMAGES}")
        
        # Test 1: Google search for a power plant
        print(f"\n🔍 Testing Google search...")
        test_plant = "Stanwell Power Station"
        
        if image_extraction.SCRAPER_API_KEY:
            search_results = image_extraction.search_google(test_plant, 1)
            print(f"   Found {len(search_results)} search results:")
            
            wikipedia_count = 0
            non_wikipedia_count = 0
            
            for i, url in enumerate(search_results, 1):
                parsed = urlparse(url)
                is_wikipedia = 'wikipedia.org' in parsed.netloc
                if is_wikipedia:
                    wikipedia_count += 1
                    print(f"     {i}. 🚫 WIKIPEDIA: {url}")
                else:
                    non_wikipedia_count += 1
                    print(f"     {i}. ✅ NON-WIKI: {url}")
            
            print(f"\n📊 Results Summary:")
            print(f"   Wikipedia pages: {wikipedia_count}")
            print(f"   Non-Wikipedia pages: {non_wikipedia_count}")
            print(f"   Will process: {min(non_wikipedia_count, image_extraction.MAX_SITES_TO_PROCESS)} sites")
            
            # Test 2: Simulate the filtering logic
            print(f"\n🎯 Testing filtering logic...")
            non_wikipedia_urls = []
            for url in search_results:
                parsed_url = urlparse(url)
                if 'wikipedia.org' not in parsed_url.netloc:
                    non_wikipedia_urls.append(url)
                    if len(non_wikipedia_urls) >= image_extraction.MAX_SITES_TO_PROCESS:
                        break
            
            print(f"   Selected {len(non_wikipedia_urls)} non-Wikipedia URLs:")
            for i, url in enumerate(non_wikipedia_urls, 1):
                parsed = urlparse(url)
                print(f"     {i}. {parsed.netloc}")
            
            # Test 3: Test internal link extraction for first site
            if non_wikipedia_urls:
                print(f"\n🔗 Testing internal link extraction...")
                test_url = non_wikipedia_urls[0]
                parsed_url = urlparse(test_url)
                base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
                
                print(f"   Testing site: {parsed_url.netloc}")
                internal_links = image_extraction.get_internal_links(base_url, test_url)
                print(f"   Found {len(internal_links)} internal links (max: {image_extraction.MAX_INTERNAL_LINKS})")
                
                if internal_links:
                    print("   Sample internal links:")
                    for i, link in enumerate(internal_links[:3], 1):
                        print(f"     {i}. {link}")
                    if len(internal_links) > 3:
                        print(f"     ... and {len(internal_links) - 3} more")
            
            return True
            
        else:
            print("⚠️ Scraper API key not set - skipping search test")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_link_calculation():
    """Test the new link calculation logic."""
    print(f"\n📈 Testing Link Calculation Logic")
    print("=" * 60)
    
    try:
        import image_extraction
        
        # Simulate different scenarios
        scenarios = [
            {
                "name": "Scenario 1: No Wikipedia in results",
                "search_results": [
                    "https://example1.com/power-plant",
                    "https://example2.com/energy",
                    "https://example3.com/coal-plant",
                    "https://example4.com/electricity",
                    "https://example5.com/generation"
                ]
            },
            {
                "name": "Scenario 2: Wikipedia first result",
                "search_results": [
                    "https://en.wikipedia.org/wiki/Power_Plant",
                    "https://example1.com/power-plant",
                    "https://example2.com/energy",
                    "https://example3.com/coal-plant",
                    "https://example4.com/electricity"
                ]
            },
            {
                "name": "Scenario 3: Multiple Wikipedia results",
                "search_results": [
                    "https://en.wikipedia.org/wiki/Power_Plant",
                    "https://example1.com/power-plant",
                    "https://en.wikipedia.org/wiki/Coal_Power",
                    "https://example2.com/energy",
                    "https://example3.com/coal-plant"
                ]
            }
        ]
        
        for scenario in scenarios:
            print(f"\n🎭 {scenario['name']}")
            search_results = scenario['search_results']
            
            # Apply filtering logic
            non_wikipedia_urls = []
            wikipedia_count = 0
            
            for url in search_results:
                from urllib.parse import urlparse
                parsed_url = urlparse(url)
                if 'wikipedia.org' in parsed_url.netloc:
                    wikipedia_count += 1
                else:
                    non_wikipedia_urls.append(url)
                    if len(non_wikipedia_urls) >= image_extraction.MAX_SITES_TO_PROCESS:
                        break
            
            # Calculate total links
            total_links = 0
            for url in non_wikipedia_urls:
                # 1 main page + MAX_INTERNAL_LINKS internal links
                total_links += 1 + image_extraction.MAX_INTERNAL_LINKS
            
            print(f"   Total search results: {len(search_results)}")
            print(f"   Wikipedia pages (ignored): {wikipedia_count}")
            print(f"   Non-Wikipedia pages: {len(search_results) - wikipedia_count}")
            print(f"   Sites to process: {len(non_wikipedia_urls)}")
            print(f"   Estimated total links: {total_links}")
            print(f"   Links per site: 1 main + {image_extraction.MAX_INTERNAL_LINKS} internal = {1 + image_extraction.MAX_INTERNAL_LINKS}")
        
        return True
        
    except Exception as e:
        print(f"❌ Link calculation test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Updated Image Extraction Logic")
    print("🚫 Wikipedia pages will be ignored")
    print("🎯 Only first 2 non-Wikipedia sites will be processed")
    print("🔗 Up to 10 internal links per site will be extracted")
    print("=" * 80)
    
    # Test Wikipedia filtering
    test1_success = test_wikipedia_filtering()
    
    # Test link calculation
    test2_success = test_link_calculation()
    
    # Summary
    print(f"\n" + "=" * 80)
    print("📋 Test Results Summary:")
    print(f"   Wikipedia Filtering Test: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(f"   Link Calculation Test: {'✅ PASS' if test2_success else '❌ FAIL'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed!")
        print("\n📋 Updated Logic Summary:")
        print("   ✅ Wikipedia pages are completely ignored")
        print("   ✅ First 2 non-Wikipedia sites are processed")
        print("   ✅ Up to 10 internal links per site are extracted")
        print("   ✅ Maximum ~22 links total (2 sites × 11 links each)")
    else:
        print("\n⚠️ Some tests failed - check output above")

if __name__ == "__main__":
    main()
