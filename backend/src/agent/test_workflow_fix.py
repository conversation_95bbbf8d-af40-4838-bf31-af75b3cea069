#!/usr/bin/env python3
"""
Test script to verify that the workflow fixes are working correctly.
This simulates the key steps that were failing in the terminal output.
"""

import os
import sys
import json
from typing import Dict, Any

# Add the agent directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_environment_loading():
    """Test that environment variables are loaded correctly."""
    print("🔍 Testing Environment Variable Loading...")
    
    try:
        # Import modules that load environment variables
        from json_s3_storage import check_s3_connection
        import os
        
        # Check critical environment variables
        env_vars = {
            'GEMINI_API_KEY': os.getenv('GEMINI_API_KEY'),
            'S3_AWS_ACCESS_KEY_ID': os.getenv('S3_AWS_ACCESS_KEY_ID'),
            'S3_AWS_SECRET_ACCESS_KEY': os.getenv('S3_AWS_SECRET_ACCESS_KEY'),
            'USE_HIERARCHICAL_S3': os.getenv('USE_HIERARCHICAL_S3', 'false')
        }
        
        print("✅ Environment variables loaded:")
        for key, value in env_vars.items():
            status = "SET" if value else "NOT SET"
            print(f"   {key}: {status}")
        
        # Test S3 connection
        s3_result = check_s3_connection('test')
        print(f"✅ S3 Connection: {'SUCCESS' if s3_result else 'FAILED'}")
        
        return all([env_vars['GEMINI_API_KEY'], env_vars['S3_AWS_ACCESS_KEY_ID'], 
                   env_vars['S3_AWS_SECRET_ACCESS_KEY']]) and s3_result
        
    except Exception as e:
        print(f"❌ Environment loading test failed: {e}")
        return False

def test_database_workflow():
    """Test the database workflow for new plants."""
    print("\n🔍 Testing Database Workflow for New Plants...")
    
    try:
        from database_manager import get_database_manager
        
        db_manager = get_database_manager()
        
        # Test 1: Database connection
        db_result = db_manager.test_connection()
        print(f"✅ Database Connection: {'SUCCESS' if db_result else 'FAILED'}")
        
        # Test 2: Check for non-existent plant (this should fail - that's correct)
        test_plant = "seil power plant"
        metadata = db_manager.get_plant_s3_metadata(test_plant)
        
        if metadata:
            print(f"✅ Plant '{test_plant}' found in database:")
            print(f"   Country: {metadata['country']}")
            print(f"   Org UUID: {metadata['org_uuid']}")
            print(f"   Plant UUID: {metadata['plant_uuid']}")
        else:
            print(f"✅ Plant '{test_plant}' not found in database (EXPECTED for new plants)")
            print("   This triggers the UID generation and database population workflow")
        
        return db_result
        
    except Exception as e:
        print(f"❌ Database workflow test failed: {e}")
        return False

def test_hierarchical_s3_workflow():
    """Test the hierarchical S3 storage workflow."""
    print("\n🔍 Testing Hierarchical S3 Storage Workflow...")
    
    try:
        from json_s3_storage import (
            generate_hierarchical_s3_path,
            sanitize_filename,
            get_hierarchical_s3_urls
        )
        
        # Test with an existing plant
        test_plant = "Stanwell Power Station"
        
        # Test path generation for different data types
        test_cases = [
            ("organization", "org_details"),
            ("plant", "plant#coal#1"),
            ("unit", "unit#coal#1#plant#1"),
            ("transition", "transition_plan")
        ]
        
        print("✅ Testing hierarchical path generation:")
        for data_type, sk_value in test_cases:
            path = generate_hierarchical_s3_path(test_plant, data_type, sk_value, "test")
            if path:
                print(f"   {data_type.upper()}: {path}")
            else:
                print(f"   ❌ Failed to generate path for {data_type}")
                return False
        
        # Test URL structure generation
        url_structure = get_hierarchical_s3_urls(test_plant, "test")
        if url_structure:
            print("✅ URL structure generated successfully")
            print(f"   Base URL: {url_structure.get('base_url', 'N/A')}")
            print(f"   Organization URL: {url_structure.get('organization', 'N/A')}")
        else:
            print("❌ Failed to generate URL structure")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Hierarchical S3 workflow test failed: {e}")
        return False

def test_gemini_model_configuration():
    """Test that Gemini model configuration is fixed."""
    print("\n🔍 Testing Gemini Model Configuration...")
    
    try:
        from configuration import Configuration
        
        config = Configuration()
        
        print("✅ Model configuration loaded:")
        print(f"   Reflection Model: {config.reflection_model}")
        print(f"   Reasoning Model: {config.reasoning_model}")
        
        # Check that we're not using the problematic model
        problematic_model = "gemini-2.5-flash-preview-04-17"
        if config.reflection_model == problematic_model:
            print(f"❌ Still using problematic model: {problematic_model}")
            return False
        
        print("✅ Model configuration is correct (no problematic models)")
        return True
        
    except Exception as e:
        print(f"❌ Gemini model configuration test failed: {e}")
        return False

def simulate_new_plant_workflow():
    """Simulate the workflow for a new plant to verify fixes."""
    print("\n🔍 Simulating New Plant Workflow...")
    
    try:
        # Step 1: Plant lookup (should fail for new plants)
        print("Step 1: Plant Registry Check")
        from database_manager import get_database_manager
        db_manager = get_database_manager()
        
        new_plant = "Test New Power Plant"
        metadata = db_manager.get_plant_s3_metadata(new_plant)
        
        if not metadata:
            print("✅ Plant not found in registry (expected for new plants)")
            
            # Step 2: UID Generation (simulated)
            print("Step 2: UID Generation")
            import uuid
            org_uid = f"ORG_TEST_{uuid.uuid4().hex[:8].upper()}"
            plant_uuid = str(uuid.uuid4())
            print(f"✅ Generated ORG UID: {org_uid}")
            print(f"✅ Generated Plant UUID: {plant_uuid}")
            
            # Step 3: S3 Path Generation
            print("Step 3: S3 Path Generation")
            from json_s3_storage import sanitize_filename
            
            # Simulate hierarchical paths
            country = "TestCountry"
            sample_paths = {
                "organization": f"{country}/{org_uid}/{org_uid}.json",
                "plant": f"{country}/{org_uid}/{plant_uuid}/plant_coal_1.json",
                "unit": f"{country}/{org_uid}/{plant_uuid}/unit_coal_1_plant_1.json",
                "transition": f"{country}/{org_uid}/{plant_uuid}/transition_plan.json"
            }
            
            for data_type, path in sample_paths.items():
                print(f"✅ {data_type.upper()} path: {path}")
            
            print("✅ New plant workflow simulation completed successfully")
            return True
        else:
            print("ℹ️ Plant found in registry (would use existing data)")
            return True
            
    except Exception as e:
        print(f"❌ New plant workflow simulation failed: {e}")
        return False

def main():
    """Run all workflow tests."""
    print("🧪 Testing Workflow Fixes")
    print("=" * 60)
    
    tests = [
        ("Environment Loading", test_environment_loading),
        ("Database Workflow", test_database_workflow),
        ("Hierarchical S3 Workflow", test_hierarchical_s3_workflow),
        ("Gemini Model Configuration", test_gemini_model_configuration),
        ("New Plant Workflow Simulation", simulate_new_plant_workflow)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All workflow fixes are working correctly!")
        print("\n📋 Key Issues Fixed:")
        print("   ✅ Environment variables loading correctly")
        print("   ✅ S3 connection working")
        print("   ✅ Database connection working")
        print("   ✅ Hierarchical S3 storage implemented")
        print("   ✅ Gemini model configuration fixed")
        print("   ✅ New plant workflow ready")
    else:
        print("⚠️ Some issues remain - check the failed tests above")

if __name__ == "__main__":
    main()
