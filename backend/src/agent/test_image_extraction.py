#!/usr/bin/env python3
"""
Test script for image extraction functionality.
Tests the module with and without heavy ML dependencies.
"""

import os
import sys

# Add the agent directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """Test basic imports without heavy ML libraries."""
    print("🔍 Testing Basic Imports...")
    
    try:
        import requests
        print("✅ requests imported")
    except Exception as e:
        print(f"❌ requests failed: {e}")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✅ BeautifulSoup imported")
    except Exception as e:
        print(f"❌ BeautifulSoup failed: {e}")
        return False
    
    try:
        import boto3
        print("✅ boto3 imported")
    except Exception as e:
        print(f"❌ boto3 failed: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ dotenv imported")
    except Exception as e:
        print(f"❌ dotenv failed: {e}")
        return False
    
    return True

def test_heavy_dependencies():
    """Test heavy ML dependencies."""
    print("\n🔍 Testing Heavy ML Dependencies...")
    
    results = {}
    
    try:
        import mediapipe as mp
        print("✅ mediapipe imported")
        results['mediapipe'] = True
    except Exception as e:
        print(f"❌ mediapipe failed: {e}")
        results['mediapipe'] = False
    
    try:
        import cv2
        print("✅ opencv imported")
        results['opencv'] = True
    except Exception as e:
        print(f"❌ opencv failed: {e}")
        results['opencv'] = False
    
    try:
        import easyocr
        print("✅ easyocr imported")
        results['easyocr'] = True
    except Exception as e:
        print(f"❌ easyocr failed: {e}")
        results['easyocr'] = False
    
    return results

def test_environment_config():
    """Test environment configuration."""
    print("\n🔍 Testing Environment Configuration...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv(os.path.join(os.path.dirname(__file__), '../../.env'))
        
        env_vars = {
            'S3_AWS_ACCESS_KEY_ID': os.getenv('S3_AWS_ACCESS_KEY_ID'),
            'S3_AWS_SECRET_ACCESS_KEY': os.getenv('S3_AWS_SECRET_ACCESS_KEY'),
            'SCRAPER_API_KEY': os.getenv('SCRAPER_API_KEY'),
            'AWS_REGION': os.getenv('AWS_REGION', 'ap-south-1')
        }
        
        print("Environment variables:")
        for key, value in env_vars.items():
            status = "SET" if value else "NOT SET"
            print(f"   {key}: {status}")
        
        return all([env_vars['S3_AWS_ACCESS_KEY_ID'], env_vars['S3_AWS_SECRET_ACCESS_KEY']])
        
    except Exception as e:
        print(f"❌ Environment config test failed: {e}")
        return False

def test_s3_connection():
    """Test S3 connection."""
    print("\n🔍 Testing S3 Connection...")
    
    try:
        import boto3
        from dotenv import load_dotenv
        
        load_dotenv(os.path.join(os.path.dirname(__file__), '../../.env'))
        
        s3_client = boto3.client(
            's3',
            aws_access_key_id=os.getenv('S3_AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('S3_AWS_SECRET_ACCESS_KEY'),
            region_name=os.getenv('AWS_REGION', 'ap-south-1')
        )
        
        # Test connection by checking bucket
        bucket_name = 'clem-transition-tech'
        s3_client.head_bucket(Bucket=bucket_name)
        print(f"✅ S3 connection successful to bucket: {bucket_name}")
        return True
        
    except Exception as e:
        print(f"❌ S3 connection failed: {e}")
        return False

def test_basic_web_scraping():
    """Test basic web scraping functionality."""
    print("\n🔍 Testing Basic Web Scraping...")
    
    try:
        import requests
        from bs4 import BeautifulSoup
        from urllib.parse import urljoin, urlparse
        
        # Test with a simple webpage
        test_url = "https://httpbin.org/html"
        response = requests.get(test_url, timeout=10)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, "html.parser")
            print("✅ Web scraping basic functionality works")
            return True
        else:
            print(f"❌ Web scraping failed - status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web scraping test failed: {e}")
        return False

def create_lightweight_image_extractor():
    """Create a lightweight version of image extraction without ML dependencies."""
    print("\n🔍 Creating Lightweight Image Extractor...")
    
    lightweight_code = '''
def lightweight_extract_images(powerplant: str, session_id: str = "unknown") -> list:
    """
    Lightweight image extraction without ML dependencies.
    Only downloads and uploads images without filtering.
    """
    import requests
    import boto3
    import os
    from bs4 import BeautifulSoup
    from urllib.parse import urljoin, urlparse
    from dotenv import load_dotenv
    
    print(f"[Session {session_id}] 🖼️ Starting lightweight image extraction for: {powerplant}")
    
    try:
        # Load environment
        load_dotenv(os.path.join(os.path.dirname(__file__), '../../.env'))
        
        # Simple Google search simulation (without ScraperAPI)
        # In production, you would use the actual search function
        print(f"[Session {session_id}] ⚠️ Using mock search results for testing")
        
        # Mock some image URLs for testing
        test_images = [
            "https://httpbin.org/image/png",
            "https://httpbin.org/image/jpeg"
        ]
        
        # Create local folder
        image_folder = f"{powerplant.replace(' ', '_')}_images_{session_id}"
        os.makedirs(image_folder, exist_ok=True)
        
        # Download test images
        downloaded_files = []
        for i, img_url in enumerate(test_images):
            try:
                response = requests.get(img_url, timeout=10)
                if response.status_code == 200:
                    filename = f"test_image_{i}.png"
                    filepath = os.path.join(image_folder, filename)
                    with open(filepath, 'wb') as f:
                        f.write(response.content)
                    downloaded_files.append(filepath)
                    print(f"[Session {session_id}] ✅ Downloaded: {filename}")
            except Exception as e:
                print(f"[Session {session_id}] ❌ Failed to download image: {e}")
        
        # Upload to S3 (mock)
        print(f"[Session {session_id}] 📤 Would upload {len(downloaded_files)} images to S3")
        
        # Clean up
        import shutil
        if os.path.exists(image_folder):
            shutil.rmtree(image_folder)
        
        # Return mock S3 URLs
        mock_s3_urls = [
            f"https://clem-transition-tech.s3.amazonaws.com/{powerplant}_images/test_image_{i}.png"
            for i in range(len(downloaded_files))
        ]
        
        print(f"[Session {session_id}] ✅ Lightweight extraction completed")
        return mock_s3_urls
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ Lightweight extraction failed: {e}")
        return []
'''
    
    print("✅ Lightweight image extractor code generated")
    return lightweight_code

def main():
    """Run all image extraction tests."""
    print("🧪 Testing Image Extraction Module")
    print("=" * 60)
    
    # Test basic imports
    basic_ok = test_basic_imports()
    
    # Test heavy dependencies
    heavy_deps = test_heavy_dependencies()
    
    # Test environment
    env_ok = test_environment_config()
    
    # Test S3 connection
    s3_ok = test_s3_connection()
    
    # Test web scraping
    scraping_ok = test_basic_web_scraping()
    
    # Create lightweight version
    lightweight_code = create_lightweight_image_extractor()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Image Extraction Test Results:")
    print(f"   Basic Imports: {'✅ PASS' if basic_ok else '❌ FAIL'}")
    print(f"   Environment Config: {'✅ PASS' if env_ok else '❌ FAIL'}")
    print(f"   S3 Connection: {'✅ PASS' if s3_ok else '❌ FAIL'}")
    print(f"   Web Scraping: {'✅ PASS' if scraping_ok else '❌ FAIL'}")
    
    print(f"\n📋 ML Dependencies:")
    for dep, available in heavy_deps.items():
        status = "✅ AVAILABLE" if available else "❌ NOT AVAILABLE"
        print(f"   {dep}: {status}")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    if not all(heavy_deps.values()):
        print("   ⚠️ Some ML dependencies are missing")
        print("   📝 Consider using lightweight version without ML filtering")
        print("   📝 Or install missing dependencies:")
        if not heavy_deps['mediapipe']:
            print("      pip install mediapipe")
        if not heavy_deps['opencv']:
            print("      pip install opencv-python")
        if not heavy_deps['easyocr']:
            print("      pip install easyocr")
    else:
        print("   ✅ All dependencies available - full functionality enabled")
    
    if basic_ok and env_ok and s3_ok:
        print("   ✅ Core image extraction functionality is ready")
    else:
        print("   ❌ Core functionality has issues - check failed tests")

if __name__ == "__main__":
    main()
